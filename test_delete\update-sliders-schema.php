<?php
/**
 * Update Sliders Table Schema
 * Adds new columns for enhanced slider functionality
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

try {
    $db = Database::getConnection();
    
    echo "=== UPDATING SLIDERS TABLE SCHEMA ===\n\n";
    
    // Check if new columns already exist
    $stmt = $db->query("DESCRIBE sliders");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $new_columns = [
        'text_color' => "VARCHAR(7) DEFAULT '#ffffff'",
        'overlay_color' => "VARCHAR(7) DEFAULT '#000000'", 
        'overlay_opacity' => "DECIMAL(3,2) DEFAULT 0.50"
    ];
    
    foreach ($new_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $columns)) {
            $sql = "ALTER TABLE sliders ADD COLUMN $column_name $column_definition";
            $db->exec($sql);
            echo "✅ Added column: $column_name\n";
        } else {
            echo "ℹ️  Column already exists: $column_name\n";
        }
    }
    
    echo "\n=== UPDATING EXISTING SLIDERS WITH DEFAULT VALUES ===\n\n";
    
    // Update existing sliders with default values
    $update_sql = "
        UPDATE sliders SET 
            text_color = COALESCE(text_color, '#ffffff'),
            overlay_color = COALESCE(overlay_color, '#000000'),
            overlay_opacity = COALESCE(overlay_opacity, 0.50)
        WHERE text_color IS NULL OR overlay_color IS NULL OR overlay_opacity IS NULL
    ";
    
    $result = $db->exec($update_sql);
    echo "✅ Updated $result existing sliders with default styling values\n";
    
    echo "\n=== SCHEMA UPDATE COMPLETE ===\n\n";
    echo "The sliders table now supports:\n";
    echo "- Custom text colors\n";
    echo "- Custom overlay colors\n"; 
    echo "- Adjustable overlay opacity\n";
    echo "\nYou can now use the admin slider management page to customize these settings.\n";
    
} catch (Exception $e) {
    echo "❌ Error updating sliders schema: " . $e->getMessage() . "\n";
}
?>
