<?php
/**
 * Slider Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_slider':
                try {
                    $db = Database::getConnection();
                    
                    // Handle background image upload
                    $background_image = '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }
                    
                    $stmt = $db->prepare("
                        INSERT INTO sliders (title, subtitle, background_image, button_text, button_link, 
                                           text_color, overlay_color, overlay_opacity, sort_order, active) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $_POST['title'],
                        $_POST['subtitle'],
                        $background_image,
                        $_POST['button_text'],
                        $_POST['button_link'],
                        $_POST['text_color'] ?? '#ffffff',
                        $_POST['overlay_color'] ?? '#000000',
                        $_POST['overlay_opacity'] ?? 0.5,
                        $_POST['sort_order'] ?? 0,
                        isset($_POST['active']) ? 1 : 0
                    ]);
                    
                    $message = 'Slider added successfully!';
                } catch (Exception $e) {
                    $error = 'Error adding slider: ' . $e->getMessage();
                }
                break;
                
            case 'update_slider':
                try {
                    $db = Database::getConnection();
                    
                    // Handle background image upload
                    $background_image = $_POST['current_background_image'] ?? '';
                    if (isset($_FILES['background_image']) && $_FILES['background_image']['error'] === UPLOAD_ERR_OK) {
                        $upload_result = uploadFile($_FILES['background_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload_result['success']) {
                            $background_image = $upload_result['url'];
                        } else {
                            $error = 'Image upload failed: ' . $upload_result['message'];
                            break;
                        }
                    }
                    
                    $stmt = $db->prepare("
                        UPDATE sliders SET title = ?, subtitle = ?, background_image = ?, button_text = ?, 
                                         button_link = ?, text_color = ?, overlay_color = ?, overlay_opacity = ?, 
                                         sort_order = ?, active = ? 
                        WHERE id = ?
                    ");
                    
                    $stmt->execute([
                        $_POST['title'],
                        $_POST['subtitle'],
                        $background_image,
                        $_POST['button_text'],
                        $_POST['button_link'],
                        $_POST['text_color'] ?? '#ffffff',
                        $_POST['overlay_color'] ?? '#000000',
                        $_POST['overlay_opacity'] ?? 0.5,
                        $_POST['sort_order'] ?? 0,
                        isset($_POST['active']) ? 1 : 0,
                        $_POST['slider_id']
                    ]);
                    
                    $message = 'Slider updated successfully!';
                } catch (Exception $e) {
                    $error = 'Error updating slider: ' . $e->getMessage();
                }
                break;
                
            case 'delete_slider':
                try {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("DELETE FROM sliders WHERE id = ?");
                    $stmt->execute([$_POST['slider_id']]);
                    $message = 'Slider deleted successfully!';
                } catch (Exception $e) {
                    $error = 'Error deleting slider: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all sliders
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM sliders ORDER BY sort_order ASC, id ASC");
    $sliders = $stmt->fetchAll();
} catch (Exception $e) {
    $sliders = [];
    $error = 'Error fetching sliders: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slider Management - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #34495e;
            padding: 0;
            display: flex;
            overflow-x: auto;
        }
        
        .admin-nav a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 1rem 1.5rem;
            white-space: nowrap;
            transition: all 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #E67E22;
            color: white;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .btn {
            background: #E67E22;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #d35400;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .slider-item {
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }
        
        .slider-header {
            background: #f8f9fa;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .slider-preview {
            padding: 1rem;
            display: grid;
            grid-template-columns: 200px 1fr auto;
            gap: 1rem;
            align-items: center;
        }
        
        .slider-image {
            width: 200px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .slider-info h4 {
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .slider-info p {
            color: #7f8c8d;
            margin-bottom: 0.25rem;
        }
        
        .slider-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .color-input {
            width: 60px !important;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .range-input {
            width: 100%;
        }
        
        .toggle-form {
            display: none;
            padding: 1rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .toggle-form.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .admin-content {
                padding: 1rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .slider-preview {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .slider-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Slider Management</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php" class="active">Sliders</a>
        <a href="hero-sections.php">Hero Sections</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Add New Slider -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>Add New Slider</h2>
                <button type="button" class="btn" onclick="toggleForm('add-slider-form')">Add Slider</button>
            </div>
            
            <div id="add-slider-form" class="toggle-form">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_slider">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">Slide Title</label>
                            <input type="text" id="title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number" id="sort_order" name="sort_order" value="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="subtitle">Slide Description</label>
                        <textarea id="subtitle" name="subtitle" placeholder="Enter slide description/subtitle"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="background_image">Background Image</label>
                        <input type="file" id="background_image" name="background_image" accept="image/*" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="button_text">Button Text</label>
                            <input type="text" id="button_text" name="button_text" value="Learn More">
                        </div>
                        <div class="form-group">
                            <label for="button_link">Button Link</label>
                            <input type="text" id="button_link" name="button_link" placeholder="e.g., projects, about, contact">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="text_color">Text Color</label>
                            <input type="color" id="text_color" name="text_color" value="#ffffff" class="color-input">
                        </div>
                        <div class="form-group">
                            <label for="overlay_color">Overlay Color</label>
                            <input type="color" id="overlay_color" name="overlay_color" value="#000000" class="color-input">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="overlay_opacity">Overlay Opacity</label>
                        <input type="range" id="overlay_opacity" name="overlay_opacity" min="0" max="1" step="0.1" value="0.5" class="range-input">
                        <span id="opacity-value">0.5</span>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="active" checked> Active
                        </label>
                    </div>
                    
                    <button type="submit" class="btn">Add Slider</button>
                </form>
            </div>
        </div>

        <!-- Existing Sliders -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h2>Manage Sliders (<?php echo count($sliders); ?>)</h2>
            </div>

            <div class="admin-card-body">
                <?php if (empty($sliders)): ?>
                    <p>No sliders found. Add your first slider above.</p>
                <?php else: ?>
                    <?php foreach ($sliders as $slider): ?>
                        <div class="slider-item">
                            <div class="slider-preview">
                                <img src="<?php echo ensureAbsoluteUrl($slider['background_image']); ?>"
                                     alt="<?php echo htmlspecialchars($slider['title']); ?>"
                                     class="slider-image">

                                <div class="slider-info">
                                    <h4><?php echo htmlspecialchars($slider['title']); ?></h4>
                                    <p><strong>Description:</strong> <?php echo htmlspecialchars(substr($slider['subtitle'], 0, 100)) . (strlen($slider['subtitle']) > 100 ? '...' : ''); ?></p>
                                    <p><strong>Button:</strong> <?php echo htmlspecialchars($slider['button_text']); ?> → <?php echo htmlspecialchars($slider['button_link']); ?></p>
                                    <p><strong>Order:</strong> <?php echo $slider['sort_order']; ?> | <strong>Status:</strong> <?php echo $slider['active'] ? 'Active' : 'Inactive'; ?></p>
                                </div>

                                <div class="slider-actions">
                                    <button type="button" class="btn" onclick="toggleForm('edit-slider-<?php echo $slider['id']; ?>')">Edit</button>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this slider?')">
                                        <input type="hidden" name="action" value="delete_slider">
                                        <input type="hidden" name="slider_id" value="<?php echo $slider['id']; ?>">
                                        <button type="submit" class="btn btn-danger">Delete</button>
                                    </form>
                                </div>
                            </div>

                            <!-- Edit Form -->
                            <div id="edit-slider-<?php echo $slider['id']; ?>" class="toggle-form">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="update_slider">
                                    <input type="hidden" name="slider_id" value="<?php echo $slider['id']; ?>">
                                    <input type="hidden" name="current_background_image" value="<?php echo htmlspecialchars($slider['background_image']); ?>">

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="edit_title_<?php echo $slider['id']; ?>">Slide Title</label>
                                            <input type="text" id="edit_title_<?php echo $slider['id']; ?>" name="title" value="<?php echo htmlspecialchars($slider['title']); ?>" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="edit_sort_order_<?php echo $slider['id']; ?>">Sort Order</label>
                                            <input type="number" id="edit_sort_order_<?php echo $slider['id']; ?>" name="sort_order" value="<?php echo $slider['sort_order']; ?>">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit_subtitle_<?php echo $slider['id']; ?>">Slide Description</label>
                                        <textarea id="edit_subtitle_<?php echo $slider['id']; ?>" name="subtitle"><?php echo htmlspecialchars($slider['subtitle']); ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit_background_image_<?php echo $slider['id']; ?>">Background Image (leave empty to keep current)</label>
                                        <input type="file" id="edit_background_image_<?php echo $slider['id']; ?>" name="background_image" accept="image/*">
                                        <small>Current: <?php echo basename($slider['background_image']); ?></small>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="edit_button_text_<?php echo $slider['id']; ?>">Button Text</label>
                                            <input type="text" id="edit_button_text_<?php echo $slider['id']; ?>" name="button_text" value="<?php echo htmlspecialchars($slider['button_text']); ?>">
                                        </div>
                                        <div class="form-group">
                                            <label for="edit_button_link_<?php echo $slider['id']; ?>">Button Link</label>
                                            <input type="text" id="edit_button_link_<?php echo $slider['id']; ?>" name="button_link" value="<?php echo htmlspecialchars($slider['button_link']); ?>">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="edit_text_color_<?php echo $slider['id']; ?>">Text Color</label>
                                            <input type="color" id="edit_text_color_<?php echo $slider['id']; ?>" name="text_color" value="<?php echo $slider['text_color'] ?? '#ffffff'; ?>" class="color-input">
                                        </div>
                                        <div class="form-group">
                                            <label for="edit_overlay_color_<?php echo $slider['id']; ?>">Overlay Color</label>
                                            <input type="color" id="edit_overlay_color_<?php echo $slider['id']; ?>" name="overlay_color" value="<?php echo $slider['overlay_color'] ?? '#000000'; ?>" class="color-input">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="edit_overlay_opacity_<?php echo $slider['id']; ?>">Overlay Opacity</label>
                                        <input type="range" id="edit_overlay_opacity_<?php echo $slider['id']; ?>" name="overlay_opacity" min="0" max="1" step="0.1" value="<?php echo $slider['overlay_opacity'] ?? 0.5; ?>" class="range-input">
                                        <span class="opacity-display"><?php echo $slider['overlay_opacity'] ?? 0.5; ?></span>
                                    </div>

                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="active" <?php echo $slider['active'] ? 'checked' : ''; ?>> Active
                                        </label>
                                    </div>

                                    <button type="submit" class="btn">Update Slider</button>
                                    <button type="button" class="btn" onclick="toggleForm('edit-slider-<?php echo $slider['id']; ?>')" style="background: #95a5a6;">Cancel</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function toggleForm(formId) {
            const form = document.getElementById(formId);
            form.classList.toggle('active');
        }

        // Update opacity display
        document.addEventListener('DOMContentLoaded', function() {
            const opacityInputs = document.querySelectorAll('input[type="range"]');
            opacityInputs.forEach(input => {
                const display = input.nextElementSibling;
                input.addEventListener('input', function() {
                    if (display && display.tagName === 'SPAN') {
                        display.textContent = this.value;
                    }
                });
            });
        });
    </script>
</body>
</html>
