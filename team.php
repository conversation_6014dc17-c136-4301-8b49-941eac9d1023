<?php
/**
 * Team Page - Meet Our Expert Team
 * Features CEO details and team members
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

$pageTitle = 'Our Team - Meet the Experts Behind Monolith Design';
$pageDescription = 'Meet our talented team of architects, engineers, and designers who bring decades of experience to every project.';

// Get team members from database
$team_members = getTeamMembers();

// Check if team details are enabled
$enable_team_details = getThemeOption('enable_team_details', 0);

// Get CEO information from theme options
$ceo_info = [
    'name' => getThemeOption('ceo_name', '<PERSON>'),
    'title' => getThemeOption('ceo_title', 'Chief Executive Officer & Principal Architect'),
    'bio' => getThemeOption('ceo_bio', 'With over 25 years of experience in architectural design and construction management, <PERSON> founded Monolith Design with a vision to create spaces that inspire and endure. His expertise spans commercial, residential, and institutional projects, with a particular focus on sustainable design and innovative construction methods.\n\nAlex<PERSON> holds a Master\'s degree in Architecture from MIT and is a licensed architect in multiple states. He has been recognized by the American Institute of Architects for his contributions to sustainable design and has spoken at numerous international conferences on the future of architecture.'),
    'photo' => getThemeOption('ceo_photo', ''),
    'achievements' => getThemeOption('ceo_achievements', 'Licensed Architect in 12 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2023)\nFeatured in Architectural Digest Top 100 Architects\nOver $2B in completed project value\nTEDx Speaker on Sustainable Architecture')
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Team Page Specific CSS -->
    <style>
        /* ===== TEAM PAGE STYLES ===== */
        
        /* CEO Spotlight Section */
        .ceo-spotlight {
            padding: 8rem 0;
            background: #f8f9fa;
        }
        
        .ceo-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }
        
        .ceo-image {
            position: relative;
            max-width: 350px;
            margin: 0 auto;
        }
        
        .ceo-image img {
            width: 100%;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .ceo-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .ceo-info h2 {
            color: var(--accent-color);
            font-size: 2.8rem;
            margin-bottom: 1rem;
        }
        
        .ceo-title {
            font-size: 1.5rem;
            color: #666;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .ceo-bio {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .ceo-achievements {
            list-style: none;
            padding: 0;
            display: grid;
            gap: 1rem;
        }

        .ceo-achievements li {
            background: rgba(248, 249, 250, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border-left: 3px solid var(--accent-color);
            color: #555;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .ceo-achievements li:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .ceo-achievements .achievement-icon {
            font-size: 1.2rem;
            min-width: 1.5rem;
            text-align: center;
        }

        .ceo-achievements .more-achievements {
            color: var(--accent-color);
            font-style: italic;
            font-weight: 500;
            background: rgba(var(--accent-color-rgb), 0.1);
            border-left-color: var(--accent-color);
        }

        .ceo-achievements .more-achievements:hover {
            background: rgba(var(--accent-color-rgb), 0.15);
        }

        /* Mobile Responsive Styles for Achievements */
        @media (max-width: 768px) {
            .ceo-achievements li {
                padding: 0.8rem 1rem;
                font-size: 0.9rem;
            }

            .ceo-achievements .achievement-icon {
                font-size: 1.1rem;
                min-width: 1.3rem;
            }
        }
        
        /* Team Grid */
        .team-grid {
            padding: 6rem 0;
        }
        
        .team-grid h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: var(--text-color);
        }
        
        .team-members {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .team-member {
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            border-radius: 15px;
            position: relative;
        }
        
        .team-member:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }
        
        .team-member-image {
            height: 350px;
            overflow: hidden;
            position: relative;
            border-radius: 15px 15px 0 0;
        }
        
        .team-member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }
        
        .team-member:hover .team-member-image img {
            transform: scale(1.1);
        }
        
        .team-member-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            padding: 2rem;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .team-member:hover .team-member-overlay {
            transform: translateY(0);
        }
        
        .team-member-social {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .social-link {
            width: 45px;
            height: 45px;
            background: var(--accent-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .social-link:hover {
            transform: translateY(-5px) scale(1.1);
            background: white;
            color: var(--accent-color);
        }
        
        .team-member-info {
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
        }
        
        .team-member-name {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-weight: 600;
        }
        
        .team-member-role {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
        }
        
        .team-member-bio {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .team-member-actions {
            margin-top: 1.5rem;
        }
        
        .view-profile-btn {
            display: inline-block;
            background: var(--accent-color);
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid var(--accent-color);
        }
        
        .view-profile-btn:hover {
            background: transparent;
            color: var(--accent-color);
            transform: translateY(-2px);
        }
        
        /* Special styling for featured members */
        .team-member.featured {
            border: 3px solid var(--accent-color);
        }
        
        .team-member.featured::before {
            content: '★';
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--accent-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            z-index: 2;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .ceo-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }
            

            
            .ceo-info h2 {
                font-size: 2.2rem;
            }
        }
        
        @media (max-width: 768px) {

            
            .ceo-spotlight,
            .team-grid {
                padding: 4rem 0;
            }
            
            .team-members {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Page Hero Section -->
    <?php
    // Hero data for team page
    $hero_title = 'Our Team';
    $hero_subtitle = 'Meet the Experts Behind Monolith Design';
    $hero_background = 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=1600&h=900&fit=crop'; // Team/office background
    $hero_overlay_class = 'hero-faded-overlay';
    $breadcrumb_items = [
        ['title' => 'Home', 'url' => 'index.php'],
        ['title' => 'Team', 'url' => '']
    ];

    // Load hero template
    include 'templates/page-hero.php';
    ?>

    <!-- CEO Spotlight -->
    <section class="ceo-spotlight">
        <div class="container">
            <div class="ceo-content">
                <div class="ceo-image">
                    <?php if ($ceo_info['photo']): ?>
                        <img src="<?php echo ensureAbsoluteUrl($ceo_info['photo']); ?>" alt="<?php echo htmlspecialchars($ceo_info['name']); ?> Portrait">
                    <?php else: ?>
                        <img src="<?php echo themeUrl('images/demo-images/ceo-portrait.jpg'); ?>" alt="CEO Portrait" onerror="this.src='https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face'">
                    <?php endif; ?>
                </div>
                <div class="ceo-info">
                    <h2><?php echo htmlspecialchars($ceo_info['name']); ?></h2>
                    <div class="ceo-title"><?php echo htmlspecialchars($ceo_info['title']); ?></div>
                    <div class="ceo-bio">
                        <?php
                        // Show only the first paragraph as a brief overview
                        $bio_paragraphs = explode('\n\n', $ceo_info['bio']);
                        $first_paragraph = trim($bio_paragraphs[0]);
                        if ($first_paragraph) {
                            // Limit to first 150 characters for a teaser
                            $teaser = strlen($first_paragraph) > 150 ? substr($first_paragraph, 0, 150) . '...' : $first_paragraph;
                            echo '<p>' . htmlspecialchars($teaser) . '</p>';
                        }
                        ?>
                    </div>

                    <ul class="ceo-achievements">
                        <?php
                        // Show only first 3 achievements as highlights
                        $achievements = explode('\n', $ceo_info['achievements']);
                        $achievement_icons = [
                            'Licensed Architect' => '🏛️',
                            'LEED AP' => '🌱',
                            'AIA Gold Medal' => '🏆',
                            'Featured in Architectural Digest' => '📰',
                            'Over $2B' => '💰',
                            'TEDx Speaker' => '🎤'
                        ];

                        $count = 0;
                        foreach ($achievements as $achievement) {
                            if (trim($achievement) && $count < 3) {
                                $icon = '✓';
                                foreach ($achievement_icons as $key => $emoji) {
                                    if (strpos($achievement, $key) !== false) {
                                        $icon = $emoji;
                                        break;
                                    }
                                }
                                echo '<li><span class="achievement-icon">' . $icon . '</span> ' . htmlspecialchars(trim($achievement)) . '</li>';
                                $count++;
                            }
                        }
                        if (count($achievements) > 3) {
                            echo '<li class="more-achievements">+ ' . (count(array_filter($achievements, 'trim')) - 3) . ' more achievements</li>';
                        }
                        ?>
                    </ul>

                    <?php if ($enable_team_details): ?>
                        <div class="ceo-actions" style="margin-top: 2rem;">
                            <a href="<?php echo siteUrl('team-details?ceo=1'); ?>" class="view-profile-btn">
                                View Full Profile
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Members Grid -->
    <section class="team-section" style="background: #FDF6F0 !important; padding: 4rem 0 !important;">
        <div class="container">
            <h2>Our Professional Team</h2>
            <div class="team-members team-grid">
                <?php if (!empty($team_members)): ?>
                    <?php $enable_team_details = getThemeOption('enable_team_details', 0); ?>
                    <?php foreach ($team_members as $member): ?>
                        <div class="team-member">
                            <div class="team-member-image">
                                <?php if ($member['photo']): ?>
                                    <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>">
                                <?php else: ?>
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=500&fit=crop&crop=face" alt="<?php echo htmlspecialchars($member['name']); ?>">
                                <?php endif; ?>
                                <div class="team-member-overlay">
                                    <div class="team-member-social">
                                        <?php if ($member['linkedin_url']): ?>
                                            <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" target="_blank" class="social-link">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                                </svg>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($member['email']): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="social-link">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                                </svg>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="team-member-info">
                                <h3 class="team-member-name"><?php echo htmlspecialchars($member['name']); ?></h3>
                                <div class="team-member-role"><?php echo htmlspecialchars($member['position']); ?></div>
                                <?php if ($member['bio']): ?>
                                    <p class="team-member-bio"><?php echo htmlspecialchars(substr($member['bio'], 0, 120)) . (strlen($member['bio']) > 120 ? '...' : ''); ?></p>
                                <?php endif; ?>
                                
                                <?php if ($enable_team_details): ?>
                                    <div class="team-member-actions">
                                        <a href="<?php echo siteUrl('team-details?id=' . $member['id']); ?>" class="view-profile-btn">
                                            View Profile
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Fallback content when no team members in database -->
                    <div class="team-member">
                        <div class="team-member-image">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b643?w=400&h=500&fit=crop&crop=face" alt="Team Member">
                            <div class="team-member-overlay">
                                <div class="team-member-social">
                                    <a href="#" class="social-link">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="team-member-info">
                            <h3 class="team-member-name">Your Team</h3>
                            <div class="team-member-role">Add Team Members</div>
                            <p class="team-member-bio">Use the admin panel to add your team members and showcase your expertise.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Hero CTA Section -->
    <?php include 'templates/hero-cta.php'; ?>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
