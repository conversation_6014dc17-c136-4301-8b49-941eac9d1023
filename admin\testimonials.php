<?php
/**
 * Testimonials Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Session is already started in functions.php

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_testimonial':
                $client_name = sanitizeInput($_POST['client_name']);
                $client_company = sanitizeInput($_POST['client_company']);
                $client_position = sanitizeInput($_POST['client_position']);
                $quote = sanitizeInput($_POST['quote']);
                $active = isset($_POST['active']) ? 1 : 0;
                $client_image = '';

                // Handle image upload
                if (isset($_FILES['client_image']) && $_FILES['client_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['client_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $client_image = $upload_result['url'];
                    } else {
                        $error = 'Image upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("INSERT INTO testimonials (client_name, client_company, client_position, quote, client_image, active) VALUES (?, ?, ?, ?, ?, ?)");
                        $result = $stmt->execute([$client_name, $client_company, $client_position, $quote, $client_image, $active]);
                        
                        if ($result) {
                            $message = 'Testimonial added successfully!';
                        } else {
                            $error = 'Failed to add testimonial.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_testimonial':
                $id = intval($_POST['id']);
                $client_name = sanitizeInput($_POST['client_name']);
                $client_company = sanitizeInput($_POST['client_company']);
                $client_position = sanitizeInput($_POST['client_position']);
                $quote = sanitizeInput($_POST['quote']);
                $active = isset($_POST['active']) ? 1 : 0;
                
                // Get current image
                $stmt = $db->prepare("SELECT client_image FROM testimonials WHERE id = ?");
                $stmt->execute([$id]);
                $current = $stmt->fetch();
                $client_image = $current['client_image'];

                // Handle image upload
                if (isset($_FILES['client_image']) && $_FILES['client_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['client_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload_result['success']) {
                        $client_image = $upload_result['url'];
                    } else {
                        $error = 'Image upload failed: ' . $upload_result['message'];
                        break;
                    }
                }

                if (!$error) {
                    try {
                        $stmt = $db->prepare("UPDATE testimonials SET client_name = ?, client_company = ?, client_position = ?, quote = ?, client_image = ?, active = ? WHERE id = ?");
                        $result = $stmt->execute([$client_name, $client_company, $client_position, $quote, $client_image, $active, $id]);
                        
                        if ($result) {
                            $message = 'Testimonial updated successfully!';
                        } else {
                            $error = 'Failed to update testimonial.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete_testimonial':
                $id = intval($_POST['id']);
                try {
                    $stmt = $db->prepare("DELETE FROM testimonials WHERE id = ?");
                    $result = $stmt->execute([$id]);
                    
                    if ($result) {
                        $message = 'Testimonial deleted successfully!';
                    } else {
                        $error = 'Failed to delete testimonial.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Get all testimonials
try {
    $stmt = $db->query("SELECT * FROM testimonials ORDER BY created_at DESC");
    $testimonials = $stmt->fetchAll();
} catch (Exception $e) {
    $testimonials = [];
    $error = 'Failed to load testimonials: ' . $e->getMessage();
}

// Get testimonial for editing
$edit_testimonial = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM testimonials WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_testimonial = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials Management - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #1A1A1A;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #2c3e50;
            padding: 1rem 2rem;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            margin-right: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #E67E22;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            background: #34495e;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #E67E22;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #E67E22;
            color: white;
        }
        
        .btn-primary:hover {
            background: #d35400;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .message {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .testimonials-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .testimonials-table th,
        .testimonials-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .testimonials-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .testimonials-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .image-preview {
            max-width: 100px;
            max-height: 100px;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        
        .quote-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .form-toggle {
            display: none;
        }
        
        .form-toggle.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Testimonials Management</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>

    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="hero-sections.php">Hero Sections</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php" class="active">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>

    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Add/Edit Testimonial Form -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h3><?php echo $edit_testimonial ? 'Edit Testimonial' : 'Add New Testimonial'; ?></h3>
                <button type="button" class="btn btn-secondary btn-small" onclick="toggleForm()">
                    <?php echo $edit_testimonial ? 'Cancel Edit' : 'Add New'; ?>
                </button>
            </div>

            <div class="admin-card-body">
                <form method="POST" enctype="multipart/form-data" id="testimonialForm" class="form-toggle <?php echo $edit_testimonial ? 'active' : ''; ?>">
                    <input type="hidden" name="action" value="<?php echo $edit_testimonial ? 'update_testimonial' : 'add_testimonial'; ?>">
                    <?php if ($edit_testimonial): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_testimonial['id']; ?>">
                    <?php endif; ?>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="client_name">Client Name *</label>
                            <input type="text" id="client_name" name="client_name" required
                                   value="<?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['client_name']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="client_company">Company</label>
                            <input type="text" id="client_company" name="client_company"
                                   value="<?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['client_company']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="client_position">Position/Title</label>
                            <input type="text" id="client_position" name="client_position"
                                   value="<?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['client_position']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="client_image">Client Image</label>
                            <input type="file" id="client_image" name="client_image" accept="image/*">
                            <?php if ($edit_testimonial && $edit_testimonial['client_image']): ?>
                                <img src="<?php echo ensureAbsoluteUrl($edit_testimonial['client_image']); ?>" alt="Current Image" class="image-preview">
                            <?php endif; ?>
                        </div>

                        <div class="form-group full-width">
                            <label for="quote">Testimonial Quote *</label>
                            <textarea id="quote" name="quote" required rows="4"
                                      placeholder="Enter the testimonial quote..."><?php echo $edit_testimonial ? htmlspecialchars($edit_testimonial['quote']) : ''; ?></textarea>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="active" name="active" <?php echo (!$edit_testimonial || $edit_testimonial['active']) ? 'checked' : ''; ?>>
                                <label for="active">Active</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_testimonial ? 'Update Testimonial' : 'Add Testimonial'; ?>
                        </button>
                        <?php if ($edit_testimonial): ?>
                            <a href="testimonials.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <!-- Testimonials List -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h3>Existing Testimonials (<?php echo count($testimonials); ?>)</h3>
            </div>

            <div class="admin-card-body">
                <?php if (empty($testimonials)): ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No testimonials found. <a href="#" onclick="toggleForm()">Add your first testimonial</a>.</p>
                <?php else: ?>
                    <table class="testimonials-table">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Company/Position</th>
                                <th>Quote</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($testimonials as $testimonial): ?>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <?php if ($testimonial['client_image']): ?>
                                            <img src="<?php echo ensureAbsoluteUrl($testimonial['client_image']); ?>" alt="<?php echo htmlspecialchars($testimonial['client_name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo htmlspecialchars($testimonial['client_name']); ?></strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($testimonial['client_company']): ?>
                                        <strong><?php echo htmlspecialchars($testimonial['client_company']); ?></strong><br>
                                    <?php endif; ?>
                                    <?php if ($testimonial['client_position']): ?>
                                        <small><?php echo htmlspecialchars($testimonial['client_position']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="quote-preview" title="<?php echo htmlspecialchars($testimonial['quote']); ?>">
                                        "<?php echo htmlspecialchars($testimonial['quote']); ?>"
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $testimonial['active'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $testimonial['active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="testimonials.php?edit=<?php echo $testimonial['id']; ?>" class="btn btn-secondary btn-small">Edit</a>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this testimonial?');">
                                        <input type="hidden" name="action" value="delete_testimonial">
                                        <input type="hidden" name="id" value="<?php echo $testimonial['id']; ?>">
                                        <button type="submit" class="btn btn-danger btn-small">Delete</button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function toggleForm() {
            const form = document.getElementById('testimonialForm');
            const button = document.querySelector('.admin-card-header button');

            if (form.classList.contains('active')) {
                form.classList.remove('active');
                button.textContent = 'Add New';
                // Reset form
                form.reset();
                // Change action to add
                form.querySelector('input[name="action"]').value = 'add_testimonial';
                // Remove edit ID if exists
                const editId = form.querySelector('input[name="id"]');
                if (editId) editId.remove();
                // Update button text
                form.querySelector('button[type="submit"]').textContent = 'Add Testimonial';
                // Remove cancel button if exists
                const cancelBtn = form.querySelector('.btn-secondary');
                if (cancelBtn && cancelBtn.textContent === 'Cancel') {
                    cancelBtn.remove();
                }
            } else {
                form.classList.add('active');
                button.textContent = 'Cancel';
            }
        }

        // Auto-show form if editing
        <?php if ($edit_testimonial): ?>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('testimonialForm').classList.add('active');
        });
        <?php endif; ?>
    </script>
</body>
</html>
