<?php
/**
 * Team Member Details Page
 * Individual team member profile page
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Check if team details are enabled
$enable_team_details = getThemeOption('enable_team_details', 0);
if (!$enable_team_details) {
    header('Location: team.php');
    exit;
}

// Check if this is CEO profile or regular team member
$is_ceo = isset($_GET['ceo']) && $_GET['ceo'] == '1';
$member_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$is_ceo && !$member_id) {
    header('Location: team.php');
    exit;
}

if ($is_ceo) {
    // Get CEO information from theme options
    $member = [
        'name' => getThemeOption('ceo_name', '<PERSON>'),
        'position' => getThemeOption('ceo_title', 'Chief Executive Officer & Principal Architect'),
        'bio' => getThemeOption('ceo_bio', 'With over 25 years of experience in architectural design and construction management, <PERSON> founded Monolith Design with a vision to create spaces that inspire and endure.'),
        'photo' => getThemeOption('ceo_photo', ''),
        'email' => getThemeOption('ceo_email', ''),
        'linkedin_url' => getThemeOption('ceo_linkedin', ''),
        'achievements' => getThemeOption('ceo_achievements', 'Licensed Architect in 12 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2023)'),
        'education' => getThemeOption('ceo_education', 'Master of Architecture|Massachusetts Institute of Technology (MIT)|1998\nBachelor of Architecture|University of California, Berkeley|1996\nProfessional Licenses|Licensed Architect in 12 states including CA, NY, TX, FL|Current'),
        'philosophy_quote' => getThemeOption('ceo_philosophy_quote', 'Architecture is not just about creating buildings; it\'s about crafting experiences that inspire, spaces that endure, and environments that enhance human life.'),
        'philosophy_description' => getThemeOption('ceo_philosophy_description', 'Alexander\'s leadership approach combines visionary thinking with practical execution, ensuring that every project delivered by Monolith Design exceeds client expectations.'),
        'projects' => getThemeOption('ceo_projects', 'Metropolitan Arts Center|$150M cultural complex featuring sustainable design and innovative acoustic engineering\nSkyline Corporate Headquarters|LEED Platinum certified office tower with revolutionary energy management systems\nRiverside Residential Complex|Award-winning mixed-use development integrating affordable housing with luxury amenities')
    ];
} else {
    // Get team member data
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM team_members WHERE id = ? AND active = 1");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            header('Location: team.php');
            exit;
        }
    } catch (Exception $e) {
        header('Location: team.php');
        exit;
    }
}

$pageTitle = htmlspecialchars($member['name']) . ' - ' . htmlspecialchars($member['position']);
$pageDescription = 'Learn more about ' . htmlspecialchars($member['name']) . ', ' . htmlspecialchars($member['position']) . ' at Monolith Design.';

// Get featured projects for "Featured Work" section
try {
    $featured_projects = getProjects(null, 4); // Get 4 featured projects
} catch (Exception $e) {
    $featured_projects = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Team Details Page Specific CSS -->
    <style>
        /* ===== TEAM DETAILS PAGE STYLES ===== */
        
        /* Modern Hero Section */
        .team-detail-hero {
            background: var(--light-accent);
            padding: 6rem 0 4rem;
            margin-top: 80px; /* Account for fixed header */
            position: relative;
            overflow: hidden;
        }

        .team-detail-hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: linear-gradient(135deg, var(--accent-color) 0%, #d35400 100%);
            opacity: 0.05;
            z-index: 1;
        }

        .team-hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 3rem;
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .breadcrumb a {
            color: var(--text-light);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: var(--accent-color);
        }

        .breadcrumb-separator {
            color: var(--text-light);
            opacity: 0.5;
        }

        .hero-main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 4rem;
            align-items: start;
        }

        .hero-text-content {
            padding-top: 1rem;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 1rem;
            line-height: 1.1;
        }

        .hero-position {
            font-size: 1.25rem;
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-summary {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 2rem;
            max-width: 500px;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            display: block;
        }

        .hero-stat-label {
            font-size: 0.875rem;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-image-container {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transform: translateY(-2rem);
        }

        .hero-image {
            width: 100%;
            height: 350px;
            object-fit: cover;
            border-radius: 12px;
        }

        .hero-image-badge {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--accent-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: 0 10px 20px rgba(230, 126, 34, 0.3);
        }
        
        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .breadcrumb a:hover {
            color: white;
        }
        
        .breadcrumb span {
            color: rgba(255, 255, 255, 0.6);
        }
        
        /* Main Content */
        .team-detail-content {
            padding: 8rem 0;
        }
        
        .team-profile {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
            align-items: start;
            margin-bottom: 6rem;
        }
        
        .team-profile-image {
            position: relative;
        }
        
        .team-profile-image img {
            width: 100%;
            max-width: 400px;
            aspect-ratio: 4/5;
            object-fit: cover;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .team-profile-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .team-profile-info h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .team-role {
            font-size: 1.3rem;
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 2rem;
        }
        
        .team-bio {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .team-contact {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #f8f9fa;
            border-radius: 50px;
            text-decoration: none;
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .contact-item:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .team-expertise {
            background: #f8f9fa;
            padding: 3rem;
            border-radius: 10px;
            margin-bottom: 4rem;
        }
        
        .team-expertise h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--text-color);
        }
        
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .expertise-item {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .expertise-item:hover {
            transform: translateY(-5px);
        }
        
        .expertise-item h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        /* CEO Achievements */
        .ceo-achievements-detail {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .achievement-item {
            background: rgba(248, 249, 250, 0.8);
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-color);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            font-weight: 500;
            color: var(--text-color);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .achievement-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 0.95);
        }

        .achievement-icon {
            font-size: 1.5rem;
            min-width: 2rem;
            text-align: center;
        }

        .achievement-text {
            flex: 1;
            line-height: 1.4;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .ceo-achievements-detail {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .achievement-item {
                padding: 1rem;
                font-size: 0.9rem;
            }

            .achievement-icon {
                font-size: 1.3rem;
                min-width: 1.8rem;
            }
        }

        /* CEO Additional Sections */
        .ceo-additional-sections {
            margin-top: 2rem;
        }

        .ceo-section {
            margin-bottom: 3rem;
        }

        .ceo-section h3 {
            color: var(--accent-color);
            font-size: 2rem;
            margin-bottom: 2rem;
            padding-bottom: 0.5rem;
        }

        .education-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .education-item {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .education-item:hover {
            transform: translateY(-5px);
        }

        .education-item h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }

        .education-item p {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        .education-item .year {
            background: var(--accent-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .philosophy-content blockquote {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 10px;
            font-style: italic;
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: var(--text-color);
        }

        .philosophy-content p {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-color);
        }

        .projects-highlight {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .project-item {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .project-item:hover {
            transform: translateY(-5px);
        }

        .project-item h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .project-item p {
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Featured Work Section - Match Home Page Design */
        .featured-projects-section {
            padding: 4rem 0;
        }

        .featured-projects-section .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .featured-projects-section .subtitle {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        .featured-projects-section h3 {
            font-size: 2.5rem;
            color: var(--text-color);
            margin: 0;
        }

        .work-outer-main {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .work-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .work-main-card {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
        }

        .work-main {
            aspect-ratio: 16/10;
            overflow: hidden;
            position: relative;
            background-color: #f5f5f5;
            height: 400px;
            width: 100%;
            border-radius: 15px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: transform 0.3s;
        }

        .work-main:hover {
            transform: scale(1.02);
        }

        .work-main img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .work-main-card:hover .work-main img {
            transform: scale(1.1);
        }

        .work-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 2rem;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
        }

        .work-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .work-mini-card {
            background-color: rgba(255,255,255,0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            font-size: 0.8rem;
            font-weight: 500;
        }

        .work-content h6 {
            font-size: 1.5rem;
            margin: 0;
            font-weight: 600;
        }

        .work-list {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .work-card {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 1rem;
            padding: 1rem;
            border-radius: 10px;
            transition: all 0.3s;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.5);
            position: relative;
        }

        .work-card:hover {
            background-color: var(--secondary-color, #f8f9fa);
            transform: translateY(-2px);
        }

        .work-cover {
            aspect-ratio: 4/3;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            background-color: #f5f5f5;
            height: 180px;
            width: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .work-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .work-card:hover .work-cover img {
            transform: scale(1.1);
        }

        .work-link {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
        }
        
        /* Back Navigation */
        .back-navigation {
            margin-bottom: 3rem;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            color: var(--text-color);
            transform: translateX(-5px);
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .team-profile {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }
            
            .team-detail-hero h1 {
                font-size: 2.5rem;
            }
            
            .team-contact {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .work-wrapper {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .work-card {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
        
        @media (max-width: 768px) {
            .team-detail-hero {
                padding: 4rem 0 3rem;
            }

            .team-hero-container {
                padding: 0 1rem;
            }

            .hero-main-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-position {
                font-size: 1rem;
            }

            .hero-summary {
                font-size: 1rem;
                max-width: none;
            }

            .hero-stats {
                justify-content: center;
                gap: 1.5rem;
            }

            .hero-stat-number {
                font-size: 1.5rem;
            }

            .hero-image-container {
                transform: none;
                max-width: 280px;
                margin: 0 auto;
            }

            .hero-image {
                height: 300px;
            }

            .breadcrumb {
                margin-bottom: 2rem;
                justify-content: center;
            }

            .team-detail-content,
            .other-team-members {
                padding: 4rem 0;
            }
            
            .expertise-grid {
                grid-template-columns: 1fr;
            }
            
            .featured-projects-section {
                padding: 3rem 0;
            }
            
            .work-card {
                padding: 0.75rem;
            }
            
            .work-cover {
                height: 150px;
            }

            .work-excerpt {
                font-size: 0.8rem;
            }
        }

        /* Featured Work Excerpt Styling */
        .work-excerpt {
            font-size: 0.875rem;
            color: #666;
            line-height: 1.5;
            margin-top: 0.75rem;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Team Detail Hero Section -->
    <section class="team-detail-hero">
        <div class="team-hero-container">
            <div class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo siteUrl('team'); ?>">Team</a>
                <span class="breadcrumb-separator">›</span>
                <span><?php echo htmlspecialchars($member['name']); ?></span>
            </div>

            <div class="hero-main-content">
                <div class="hero-text-content">
                    <h1 class="hero-title"><?php echo htmlspecialchars($member['name']); ?></h1>
                    <div class="hero-position"><?php echo htmlspecialchars($member['position']); ?></div>

                    <?php if ($member['bio']): ?>
                        <div class="hero-summary">
                            <?php echo htmlspecialchars(substr($member['bio'], 0, 200)) . (strlen($member['bio']) > 200 ? '...' : ''); ?>
                        </div>
                    <?php endif; ?>

                    <div class="hero-stats">
                        <div class="hero-stat">
                            <span class="hero-stat-number">10+</span>
                            <span class="hero-stat-label">Years Experience</span>
                        </div>
                        <div class="hero-stat">
                            <span class="hero-stat-number">50+</span>
                            <span class="hero-stat-label">Projects Completed</span>
                        </div>
                        <div class="hero-stat">
                            <span class="hero-stat-number">25+</span>
                            <span class="hero-stat-label">Awards Won</span>
                        </div>
                    </div>
                </div>

                <div class="hero-image-container">
                    <?php if ($member['photo']): ?>
                        <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>" class="hero-image">
                    <?php else: ?>
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face" alt="<?php echo htmlspecialchars($member['name']); ?>" class="hero-image">
                    <?php endif; ?>
                    <div class="hero-image-badge">Team Member</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Detail Content -->
    <section class="team-detail-content">
        <div class="container">
            <div class="back-navigation">
                <a href="<?php echo siteUrl('team'); ?>" class="back-btn">
                    ← Back to Team
                </a>
            </div>
            
            <div class="team-profile">
                <div class="team-profile-image">
                    <?php if ($member['photo']): ?>
                        <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>">
                    <?php else: ?>
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face" alt="<?php echo htmlspecialchars($member['name']); ?>">
                    <?php endif; ?>
                </div>
                
                <div class="team-profile-info">
                    <h2><?php echo htmlspecialchars($member['name']); ?></h2>
                    <div class="team-role"><?php echo htmlspecialchars($member['position']); ?></div>
                    
                    <?php if ($member['bio']): ?>
                        <div class="team-bio">
                            <?php
                            if ($is_ceo) {
                                // For CEO, show full comprehensive bio with paragraphs
                                $bio_paragraphs = explode('\n\n', $member['bio']);
                                foreach ($bio_paragraphs as $paragraph) {
                                    if (trim($paragraph)) {
                                        echo '<p>' . nl2br(htmlspecialchars(trim($paragraph))) . '</p>';
                                    }
                                }
                            } else {
                                // For regular team members, show standard bio
                                echo nl2br(htmlspecialchars($member['bio']));
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="team-contact">
                        <?php if ($member['email']): ?>
                            <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="contact-item">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                </svg>
                                Email
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($member['linkedin_url']): ?>
                            <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" target="_blank" class="contact-item">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                LinkedIn
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Expertise/Achievements Section -->
            <div class="team-expertise">
                <?php if ($is_ceo): ?>
                    <h3>Achievements & Recognition</h3>
                    <div class="ceo-achievements-detail">
                        <?php
                        $achievements = explode('\n', $member['achievements']);
                        $achievement_icons = [
                            'Licensed Architect' => '🏛️',
                            'LEED AP' => '🌱',
                            'AIA Gold Medal' => '🏆',
                            'Featured in Architectural Digest' => '📰',
                            'Over $2B' => '💰',
                            'TEDx Speaker' => '🎤'
                        ];

                        foreach ($achievements as $achievement) {
                            if (trim($achievement)) {
                                $icon = '✓';
                                foreach ($achievement_icons as $key => $emoji) {
                                    if (strpos($achievement, $key) !== false) {
                                        $icon = $emoji;
                                        break;
                                    }
                                }
                                echo '<div class="achievement-item">
                                        <span class="achievement-icon">' . $icon . '</span>
                                        <span class="achievement-text">' . htmlspecialchars(trim($achievement)) . '</span>
                                      </div>';
                            }
                        }
                        ?>
                    </div>

                    <!-- CEO Additional Sections -->
                    <div class="ceo-additional-sections">
                        <div class="ceo-section">
                            <h3>Education & Credentials</h3>
                            <div class="education-grid">
                                <?php
                                $education_items = explode('\n', $member['education']);
                                foreach ($education_items as $education) {
                                    if (trim($education)) {
                                        $parts = explode('|', trim($education));
                                        if (count($parts) >= 3) {
                                            $degree = trim($parts[0]);
                                            $institution = trim($parts[1]);
                                            $year = trim($parts[2]);
                                            echo '<div class="education-item">';
                                            echo '<h4>' . htmlspecialchars($degree) . '</h4>';
                                            echo '<p>' . htmlspecialchars($institution) . '</p>';
                                            echo '<span class="year">' . htmlspecialchars($year) . '</span>';
                                            echo '</div>';
                                        }
                                    }
                                }
                                ?>
                            </div>
                        </div>

                        <div class="ceo-section">
                            <h3>Leadership Philosophy</h3>
                            <div class="philosophy-content">
                                <blockquote>
                                    "<?php echo htmlspecialchars($member['philosophy_quote']); ?>"
                                </blockquote>
                                <p><?php echo htmlspecialchars($member['philosophy_description']); ?></p>
                            </div>
                        </div>

                        <div class="ceo-section">
                            <h3>Notable Projects</h3>
                            <div class="projects-highlight">
                                <?php
                                $project_items = explode('\n', $member['projects']);
                                foreach ($project_items as $project) {
                                    if (trim($project)) {
                                        $parts = explode('|', trim($project));
                                        if (count($parts) >= 2) {
                                            $project_name = trim($parts[0]);
                                            $project_description = trim($parts[1]);
                                            echo '<div class="project-item">';
                                            echo '<h4>' . htmlspecialchars($project_name) . '</h4>';
                                            echo '<p>' . htmlspecialchars($project_description) . '</p>';
                                            echo '</div>';
                                        }
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <h3>Areas of Expertise</h3>
                    <div class="expertise-grid">
                        <div class="expertise-item">
                            <h4>Project Management</h4>
                            <p>Leading complex architectural projects from conception to completion with exceptional attention to detail and timeline management.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Sustainable Design</h4>
                            <p>Implementing eco-friendly design principles and LEED certification standards in residential and commercial projects.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Client Relations</h4>
                            <p>Building strong relationships with clients through clear communication and understanding of their unique vision and requirements.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Technical Innovation</h4>
                            <p>Utilizing cutting-edge design software and construction technologies to deliver innovative architectural solutions.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Featured Work Section - Arkify Style -->
    <section class="featured-projects-section" style="background: #FDF6F0; padding: 4rem 0;">
        <div class="container">
            <div class="section-title" style="text-align: left;">
                <div class="subtitle">
                    <div>PROJECT</div>
                </div>
                <h2>Featured Work</h2>
            </div>

            <div class="work-outer-main">
                <div class="work-wrapper">
                    <?php if (false && !empty($featured_projects)): ?>
                        <!-- Main featured project -->
                        <div class="work-main-card">
                            <div class="work-main">
                                <img src="<?php echo $featured_projects[0]['featured_image']; ?>" alt="<?php echo htmlspecialchars($featured_projects[0]['title']); ?>" class="cover-image">
                            </div>
                            <div class="work-content">
                                <div class="work-title">
                                    <div><?php echo htmlspecialchars($featured_projects[0]['location'] ?? 'Location'); ?></div>
                                    <div class="card-outer">
                                        <div class="work-mini-card">
                                            <div class="body-small"><?php echo htmlspecialchars($featured_projects[0]['category']); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <h6><?php echo htmlspecialchars($featured_projects[0]['title']); ?></h6>
                            </div>
                            <a href="<?php echo siteUrl('project/' . $featured_projects[0]['slug']); ?>" class="work-link"></a>
                        </div>

                        <!-- Secondary projects -->
                        <div class="work-list">
                            <?php for ($i = 1; $i < min(4, count($featured_projects)); $i++): ?>
                                <div class="work-card">
                                    <div class="work-cover">
                                        <img src="<?php echo $featured_projects[$i]['featured_image']; ?>" alt="<?php echo htmlspecialchars($featured_projects[$i]['title']); ?>" class="cover-image">
                                    </div>
                                    <div class="work-content">
                                        <div class="work-title">
                                            <div><?php echo htmlspecialchars($featured_projects[$i]['location'] ?? 'Location'); ?></div>
                                            <div class="work-mini-card">
                                                <div class="body-small"><?php echo htmlspecialchars($featured_projects[$i]['category']); ?></div>
                                            </div>
                                        </div>
                                        <h6><?php echo htmlspecialchars($featured_projects[$i]['title']); ?></h6>
                                    </div>
                                    <a href="<?php echo siteUrl('project/' . $featured_projects[$i]['slug']); ?>" class="work-link"></a>
                                </div>
                            <?php endfor; ?>
                        </div>
                    <?php else: ?>
                        <!-- Default projects with background images -->
                        <div class="work-main-card">
                            <div class="work-main" style="background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=building');">
                            </div>
                            <div class="work-content">
                                <div class="work-title">
                                    <div>San Francisco, CA</div>
                                    <div class="card-outer">
                                        <div class="work-mini-card">
                                            <div class="body-small">Commercial</div>
                                        </div>
                                    </div>
                                </div>
                                <h6>Modern Office Complex</h6>
                            </div>
                        </div>

                        <!-- Secondary projects - Only 2 projects -->
                        <div class="work-list">
                            <div class="work-card">
                                <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=300&fit=crop&crop=building');">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div>New York, NY</div>
                                        <div class="work-mini-card">
                                            <div class="body-small">Residential</div>
                                        </div>
                                    </div>
                                    <h6>Luxury Residential Tower</h6>
                                    <p class="work-excerpt">A stunning 45-story residential tower featuring premium amenities and breathtaking city views in the heart of Manhattan.</p>
                                </div>
                            </div>

                            <div class="work-card">
                                <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1448630360428-65456885c650?w=400&h=300&fit=crop&crop=building');">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div>Austin, TX</div>
                                        <div class="work-mini-card">
                                            <div class="body-small">Commercial</div>
                                        </div>
                                    </div>
                                    <h6>Innovation Center</h6>
                                    <p class="work-excerpt">A cutting-edge technology hub designed to foster collaboration and innovation with flexible workspaces and modern facilities.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="work-last">
                    <p>Our strategic and technical advisory service isn't based on a hunch. It's backed by years of experience, extensive technical knowledge and data-driven insights. With our trusted advice,</p>
                    <a href="<?php echo siteUrl('projects'); ?>" class="primary-button">
                        <div>VIEW ALL</div>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Hero CTA Section -->
    <?php include 'templates/hero-cta.php'; ?>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
